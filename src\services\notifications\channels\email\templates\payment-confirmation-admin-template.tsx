/**
 * Template de e-mail para notificar administradores sobre confirmação de pagamento de aluno
 * Enviado quando um aluno confirma que realizou um pagamento via PIX
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailButton,
  EmailDivider
} from './base-email-template';

export interface PaymentConfirmationAdminTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  tenantSlug?: string;
  
  // Dados do administrador
  adminName: string;
  
  // Dados do pagamento
  paymentId: string;
  amount: number;
  currency?: string;
  dueDate: string;
  description?: string;
  
  // Dados do aluno
  studentName: string;
  studentEmail: string;
  
  // Dados do plano
  planName: string;
  
  // URLs de ação
  approveUrl?: string;
  rejectUrl?: string;
  dashboardUrl?: string;
  
  // Data de confirmação
  confirmedAt: string;
}

export function PaymentConfirmationAdminTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  tenantSlug,
  adminName,
  paymentId,
  amount,
  currency = 'BRL',
  dueDate,
  description,
  studentName,
  studentEmail,
  planName,
  approveUrl,
  rejectUrl,
  dashboardUrl,
  confirmedAt
}: PaymentConfirmationAdminTemplateProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDateOnly = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={`Confirmação de Pagamento - ${studentName} - ${formatCurrency(amount)}`}
    >
      <EmailHeading level={1} color={primaryColor}>
        💰 Confirmação de Pagamento Recebida
      </EmailHeading>

      <EmailText>
        Olá <strong>{adminName}</strong>,
      </EmailText>

      <EmailText>
        Um aluno confirmou que realizou um pagamento via PIX e está aguardando sua aprovação.
      </EmailText>

      <EmailDivider color={primaryColor} />

      {/* Detalhes do pagamento */}
      <div style={{
        backgroundColor: '#f8fafc',
        padding: '24px',
        borderRadius: '8px',
        border: '2px solid #e2e8f0',
        marginBottom: '24px'
      }}>
        <EmailHeading level={2} color={primaryColor}>
          📋 Detalhes do Pagamento
        </EmailHeading>

        <div style={{ marginBottom: '16px' }}>
          <EmailText variant="small" color="#374151">
            <strong>ID do Pagamento:</strong> {paymentId}
          </EmailText>
          <EmailText variant="small" color="#374151">
            <strong>Valor:</strong> {formatCurrency(amount)}
          </EmailText>
          <EmailText variant="small" color="#374151">
            <strong>Data de Vencimento:</strong> {formatDateOnly(dueDate)}
          </EmailText>
          <EmailText variant="small" color="#374151">
            <strong>Confirmado em:</strong> {formatDate(confirmedAt)}
          </EmailText>
          {description && (
            <EmailText variant="small" color="#374151">
              <strong>Descrição:</strong> {description}
            </EmailText>
          )}
        </div>

        <EmailHeading level={3} color={secondaryColor}>
          👤 Dados do Aluno
        </EmailHeading>
        <EmailText variant="small" color="#374151">
          <strong>Nome:</strong> {studentName}
        </EmailText>
        <EmailText variant="small" color="#374151">
          <strong>Email:</strong> {studentEmail}
        </EmailText>
        <EmailText variant="small" color="#374151">
          <strong>Plano:</strong> {planName}
        </EmailText>
      </div>

      {/* Alerta de ação necessária */}
      <div style={{
        backgroundColor: '#fef3c7',
        padding: '20px',
        borderRadius: '8px',
        border: '2px solid #f59e0b',
        marginBottom: '24px'
      }}>
        <EmailHeading level={3} color="#92400e">
          ⚠️ Ação Necessária
        </EmailHeading>
        <EmailText color="#92400e">
          O aluno confirmou que realizou o pagamento via PIX. Você precisa verificar 
          se o valor foi realmente recebido em sua conta e aprovar ou rejeitar este pagamento.
        </EmailText>
      </div>

      {/* Botões de ação */}
      <div style={{ 
        textAlign: 'center', 
        margin: '32px 0',
        display: 'flex',
        gap: '16px',
        justifyContent: 'center',
        flexWrap: 'wrap'
      }}>
        {approveUrl && (
          <EmailButton href={approveUrl} primaryColor="#059669">
            ✅ Aprovar Pagamento
          </EmailButton>
        )}
        
        {rejectUrl && (
          <EmailButton href={rejectUrl} primaryColor="#dc2626">
            ❌ Rejeitar Pagamento
          </EmailButton>
        )}
      </div>

      {dashboardUrl && (
        <div style={{ textAlign: 'center', marginBottom: '24px' }}>
          <EmailButton href={dashboardUrl} primaryColor={primaryColor}>
            📊 Acessar Painel Administrativo
          </EmailButton>
        </div>
      )}

      <EmailDivider />

      {/* Instruções */}
      <EmailHeading level={3} color={primaryColor}>
        📝 Como Proceder
      </EmailHeading>

      <div style={{ marginBottom: '20px' }}>
        <EmailText variant="small">
          <strong>1.</strong> Verifique se o valor de <strong>{formatCurrency(amount)}</strong> foi recebido em sua conta PIX
        </EmailText>
        <EmailText variant="small">
          <strong>2.</strong> Confirme se o valor e os dados do pagamento estão corretos
        </EmailText>
        <EmailText variant="small">
          <strong>3.</strong> Clique em "Aprovar" se o pagamento foi recebido ou "Rejeitar" se não foi
        </EmailText>
        <EmailText variant="small">
          <strong>4.</strong> O aluno será notificado automaticamente sobre sua decisão
        </EmailText>
      </div>

      {/* Informações importantes */}
      <div style={{
        backgroundColor: '#eff6ff',
        padding: '16px',
        borderRadius: '6px',
        border: '1px solid #93c5fd',
        marginTop: '24px'
      }}>
        <EmailText variant="small" color="#1e40af">
          <strong>💡 Dica:</strong> Verifique sempre o extrato de sua conta PIX antes de aprovar 
          um pagamento. Em caso de dúvidas, entre em contato com o aluno diretamente.
        </EmailText>
      </div>

      {/* Informações de suporte */}
      <EmailText variant="small" color="#64748b">
        <strong>Precisa de ajuda?</strong> Esta notificação foi gerada automaticamente 
        quando o aluno {studentName} confirmou o pagamento. Para mais informações, 
        acesse o painel administrativo.
      </EmailText>
    </BaseEmailTemplate>
  );
}
